<script setup>
import { ref } from 'vue'
import Navbar from './components/Navbar.vue'
import Footer from './components/Footer.vue'
import Policies from './views/Policies.vue'
import Kaart from './views/Kaart.vue'
import Partijen from './views/Partijen.vue'
import MijnVragen from './views/MijnVragen.vue'

const currentPage = ref('home')

const setCurrentPage = (page) => {
  currentPage.value = page
}
</script>

<template>
  <div class="page">
    <Navbar @navigate="setCurrentPage" />

    <!-- Home Page -->
    <main v-if="currentPage === 'home'" class="main-content">
      <h1>Welkom bij Home</h1>
    </main>

    <Policies v-else-if="currentPage === 'policies'" />
    <Kaart v-else-if="currentPage === 'kaart'" />
    <Partijen v-else-if="currentPage === 'partijen'" />
    <MijnVragen v-else-if="currentPage === 'mijn-vragen'" />

    <Footer />
  </div>
</template>

<style scoped>
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 2rem;
}
</style>
