<template>
  <div class="page">
    <Navbar />
    <main class="main-content">
      <h1>We<PERSON><PERSON> bij <PERSON></h1>
    </main>
    <Footer />
  </div>
</template>

<script setup>
import Navbar from '../components/Navbar.vue'
import Footer from '../components/Footer.vue'
</script>

<style scoped>
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 2rem;
}
</style>
