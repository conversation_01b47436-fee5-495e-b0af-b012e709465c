package com.election.electionbackend.utils.xml;

/**
 * Definitions of all the known tags and their attributes.
 */
public interface TagAndAttributeNames {
    String ACCEPT_CENTRAL_SUBMISSIONS = "AcceptCentralSubmissions";
    String AFFILIATION = "Affiliation";
    String AFFILIATION_IDENTIFIER = "AffiliationIdentifier";
    String ALGORITHM = "Algorithm";
    String AUTHORITY_ADDRESS = "AuthorityAddress";
    String AUTHORITY_IDENTIFIER = "AuthorityIdentifier";
    String BELONGS_TO_SET = "BelongsToSet";
    String BELONGS_TO_COMBINATION = "BelongsToCombination";
    String CANDIDATE = "Candidate";
    String CANDIDATE_FULL_NAME = "CandidateFullName";
    String CANDIDATE_IDENTIFIER = "CandidateIdentifier";
    String CANDIDATE_LIST = "CandidateList";
    String CANONICALIZATION_METHOD = "CanonicalizationMethod";
    String CAST = "Cast";
    String COMMITTEE = "Committee";
    String COMMITTEE_CATEGORY = "CommitteeCategory";
    String COMMITTEE_NAME = "CommitteeName";
    String CONTEST = "Contest";
    String CONTEST_IDENTIFIER = "ContestIdentifier";
    String CONTEST_NAME = "ContestName";
    String CONTESTS = "Contests";
    String COUNT = "Count";
    String COUNTRY = "Country";
    String COUNTRY_NAME_CODE = "CountryNameCode";
    String CREATED_BY_AUTHORITY = "CreatedByAuthority";
    String CREATION_DATE_TIME = "CreationDateTime";
    String ELECTED = "Elected";
    String ELECTION = "Election";
    String ELECTION_CATEGORY = "ElectionCategory";
    String ELECTION_DATE = "ElectionDate";
    String ELECTION_DOMAIN = "ElectionDomain";
    String ELECTION_EVENT = "ElectionEvent";
    String ELECTION_IDENTIFIER = "ElectionIdentifier";
    String ELECTION_NAME = "ElectionName";
    String ELECTION_SUBCATEGORY = "ElectionSubcategory";
    String ELECTION_TREE = "ElectionTree";
    String EML = "EML";
    String EVENT_IDENTIFIER = "EventIdentifier";
    String FIRST_NAME = "FirstName";
    String FRYSIAN_EXPORT_ALLOWED = "FrysianExportAllowed";
    String GENDER = "Gender";
    String ID = "Id";
    String ISSUE_DATE = "IssueDate";
    String LAST_NAME = "LastName";
    String LIST_DATA = "ListData";
    String LOCALITY = "Locality";
    String LOCALITY_NAME = "LocalityName";
    String MANAGING_AUTHORITY = "ManagingAuthority";
    String MAX_VOTES = "MaxVotes";
    String NAME_LINE = "NameLine";
    String NAME_PREFIX = "NamePrefix";
    String NOMINATION_DATE = "NominationDate";
    String NUMBER_OF_SEATS = "NumberOfSeats";
    String PERSON_NAME = "PersonName";
    String PREFERENCE_THRESHOLD = "PreferenceThreshold";
    String PUBLICATION_LANGUAGE = "PublicationLanguage";
    String PUBLISH_GENDER = "PublishGender";
    String QUALIFYING_ADDRESS = "QualifyingAddress";
    String RANKING = "Ranking";
    String REASON_CODE = "ReasonCode";
    String REGISTERED_APPELLATION = "RegisteredAppellation";
    String REGISTERED_NAME = "RegisteredName";
    String REGISTERED_PARTIES = "RegisteredParties";
    String REGISTERED_PARTY = "RegisteredParty";
    String REGION = "Region";
    String REGION_CATEGORY = "RegionCategory";
    String REGION_NAME = "RegionName";
    String REGION_NUMBER = "RegionNumber";
    String REJECTED_VOTES = "RejectedVotes";
    String REPORTING_UNIT_IDENTIFIER = "ReportingUnitIdentifier";
    String REPORTING_UNIT_VOTES = "ReportingUnitVotes";
    String RESULT = "Result";
    String SELECTION = "Selection";
    String SCHEMA_LOCATION = "schemaLocation";
    String SCHEMA_VERSION = "SchemaVersion";
    String SHORT_CODE = "ShortCode";
    String SUPERIOR_REGION_CATEGORY = "SuperiorRegionCategory";
    String SUPERIOR_REGION_NUMBER  = "SuperiorRegionNumber";
    String TOTAL_VOTES = "TotalVotes";
    String TOTAL_COUNTED = "TotalCounted";
    String TRANSACTION_ID = "TransactionId";
    String TYPE = "Type";
    String NAME_TYPE = "NameType";
    String UNCOUNTED_VOTES = "UncountedVotes";
    String VALID_VOTES = "ValidVotes";
    String VOTING_METHOD = "VotingMethod";
    // These are the actual keys used for these specific tags
    String CANDIDATE_IDENTIFIER_ID = String.format("%s-%s", CANDIDATE_IDENTIFIER, "Id");
    String CANDIDATE_IDENTIFIER_SHORT_CODE = String.format("%s-%s", CANDIDATE_IDENTIFIER, "ShortCode");
}
