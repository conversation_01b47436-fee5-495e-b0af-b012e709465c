## General Definition of Done (DoD)

- All acceptance criteria of the user story have been checked off.
- You have worked according to the HBO-ICT standards (Agile Scrum, GitLab, sprint boards, sprint planning, burndown chart that closely follow the ideal line, HBO-ICT conventions, etc.)
- Written code is provided with relevant English language comments using Javadoc or TSDoc.
- The work has been reviewed by a peer.
- The code has been (functionally) tested for errors.
- The application works without errors during normal use.
- The user story is closed.

## Frontend-specific DoD

- The UX/UI part of the application adheres to the Think-Make-Check (TMC) principle.
- A design document is available and up to date.
- The application must be visible on both desktop and mobile devices.

## Backend-specific DoD

- The database is normalized up to the 3rd normal form.
- An ERD is available and up to date.
- An EERD is available and up to date.
- A class diagram is created in UML and up to date.
- All technical documentation is in English and relevant for fellow developers.
