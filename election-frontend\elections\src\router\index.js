import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Policies from '../views/Policies.vue'
import Kaart from '../views/Kaart.vue'
import Partijen from '../views/Partijen.vue'
import MijnVragen from '../views/MijnVragen.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/policies',
    name: 'Polici<PERSON>',
    component: Policies
  },
  {
    path: '/kaart',
    name: '<PERSON><PERSON>',
    component: <PERSON><PERSON>
  },
  {
    path: '/partijen',
    name: 'Partijen',
    component: Partijen
  },
  {
    path: '/mijn-vragen',
    name: 'MijnVragen',
    component: MijnVragen
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
